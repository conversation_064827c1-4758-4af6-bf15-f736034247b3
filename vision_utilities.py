"""
BlendPro Vision System Utilities
Advanced AI Vision capabilities for Blender scene analysis

Author: inkbytefo
"""

import bpy
import bmesh
import mathutils
import json
import time
import re
import base64
import io
import os
from typing import Dict, List, Any, Optional, Tuple

# Import vision dependencies with fallback handling
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    print("Warning: NumPy not available. Some vision features may be limited.")
    NUMPY_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    print("Warning: Pillow not available. Screenshot features may be limited.")
    PIL_AVAILABLE = False

try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    print("Warning: OpenCV not available. Advanced image processing features may be limited.")
    OPENCV_AVAILABLE = False

# Vision system configuration
VISION_CONFIG = {
    "default_screenshot_resolution": (1920, 1080),
    "max_objects_in_summary": 15,
    "screenshot_quality": 95,
    "vision_model_default": "gpt-4-vision-preview",
    "enable_multi_view": True,
    "enable_scene_caching": True
}

class VisionSystemError(Exception):
    """Custom exception for vision system errors"""
    pass

def check_vision_dependencies() -> Dict[str, bool]:
    """Check availability of vision system dependencies"""
    return {
        "numpy": NUMPY_AVAILABLE,
        "pillow": PIL_AVAILABLE,
        "opencv": OPENCV_AVAILABLE,
        "blender_gpu": hasattr(bpy.context, 'screen') and bpy.context.screen is not None
    }

def get_vision_system_status() -> Dict[str, Any]:
    """Get comprehensive vision system status"""
    deps = check_vision_dependencies()
    
    status = {
        "available": all(deps.values()),
        "dependencies": deps,
        "features": {
            "screenshot_capture": deps["pillow"] and deps["blender_gpu"],
            "multi_view_capture": deps["pillow"] and deps["blender_gpu"],
            "advanced_image_processing": deps["opencv"] and deps["numpy"],
            "scene_data_extraction": True,  # Always available with Blender
            "vision_api_integration": deps["pillow"]  # Requires image encoding
        },
        "config": VISION_CONFIG.copy()
    }
    
    return status

def extract_comprehensive_scene_data(context) -> Dict[str, Any]:
    """Extract detailed scene information for AI analysis"""
    try:
        scene_data = {
            "metadata": extract_scene_metadata(context),
            "objects": extract_object_data(context),
            "materials": extract_material_data(context),
            "lights": extract_lighting_data(context),
            "cameras": extract_camera_data(context),
            "world": extract_world_data(context),
            "scene_properties": extract_scene_properties(context),
            "viewport_info": extract_viewport_info(context),
            "hierarchy": extract_object_hierarchy(context)
        }
        return scene_data
    except Exception as e:
        raise VisionSystemError(f"Failed to extract scene data: {str(e)}")

def extract_scene_metadata(context) -> Dict[str, Any]:
    """Extract basic scene metadata"""
    scene = context.scene
    return {
        "blender_version": bpy.app.version_string,
        "scene_name": scene.name,
        "frame_current": scene.frame_current,
        "frame_range": [scene.frame_start, scene.frame_end],
        "render_engine": scene.render.engine,
        "timestamp": time.time(),
        "units": {
            "system": scene.unit_settings.system,
            "scale": scene.unit_settings.scale_length,
            "length_unit": scene.unit_settings.length_unit
        }
    }

def extract_object_data(context) -> List[Dict[str, Any]]:
    """Extract detailed object information"""
    objects_data = []
    
    for obj in context.scene.objects:
        try:
            obj_data = {
                "name": obj.name,
                "type": obj.type,
                "location": list(obj.location),
                "rotation_euler": list(obj.rotation_euler),
                "rotation_quaternion": list(obj.rotation_quaternion),
                "scale": list(obj.scale),
                "dimensions": list(obj.dimensions),
                "visible": obj.visible_get(),
                "hide_viewport": obj.hide_viewport,
                "hide_render": obj.hide_render,
                "material_slots": extract_object_materials(obj),
                "modifiers": extract_object_modifiers(obj),
                "constraints": extract_object_constraints(obj),
                "parent": obj.parent.name if obj.parent else None,
                "children": [child.name for child in obj.children],
                "collections": [col.name for col in obj.users_collection]
            }
            
            # Add mesh-specific data for mesh objects
            if obj.type == 'MESH' and obj.data:
                obj_data["mesh_data"] = extract_mesh_data(obj)
            
            # Add light-specific data for light objects
            elif obj.type == 'LIGHT' and obj.data:
                obj_data["light_data"] = extract_light_data(obj)
            
            # Add camera-specific data for camera objects
            elif obj.type == 'CAMERA' and obj.data:
                obj_data["camera_data"] = extract_camera_data_from_object(obj)
            
            objects_data.append(obj_data)
            
        except Exception as e:
            print(f"Warning: Failed to extract data for object {obj.name}: {e}")
            # Add minimal object data
            objects_data.append({
                "name": obj.name,
                "type": obj.type,
                "error": f"Data extraction failed: {str(e)}"
            })
    
    return objects_data

def extract_object_materials(obj) -> List[Dict[str, Any]]:
    """Extract material information from object"""
    materials = []
    
    for slot in obj.material_slots:
        if slot.material:
            materials.append({
                "name": slot.material.name,
                "slot_index": slot.slot_index,
                "link": slot.link
            })
    
    return materials

def extract_object_modifiers(obj) -> List[Dict[str, Any]]:
    """Extract modifier information from object"""
    modifiers = []
    
    for mod in obj.modifiers:
        mod_data = {
            "name": mod.name,
            "type": mod.type,
            "show_viewport": mod.show_viewport,
            "show_render": mod.show_render
        }
        
        # Add specific modifier properties based on type
        try:
            if mod.type == 'SUBSURF':
                mod_data.update({
                    "levels": mod.levels,
                    "render_levels": mod.render_levels
                })
            elif mod.type == 'MIRROR':
                mod_data.update({
                    "use_axis": [mod.use_axis[0], mod.use_axis[1], mod.use_axis[2]],
                    "mirror_object": mod.mirror_object.name if mod.mirror_object else None
                })
            elif mod.type == 'ARRAY':
                mod_data.update({
                    "count": mod.count,
                    "use_relative_offset": mod.use_relative_offset,
                    "relative_offset_displace": list(mod.relative_offset_displace)
                })
        except Exception as e:
            mod_data["extraction_error"] = str(e)
        
        modifiers.append(mod_data)
    
    return modifiers

def extract_object_constraints(obj) -> List[Dict[str, Any]]:
    """Extract constraint information from object"""
    constraints = []
    
    for constraint in obj.constraints:
        constraints.append({
            "name": constraint.name,
            "type": constraint.type,
            "influence": constraint.influence,
            "mute": constraint.mute
        })
    
    return constraints

def extract_mesh_data(obj) -> Dict[str, Any]:
    """Extract detailed mesh information"""
    try:
        mesh = obj.data
        
        # Create bmesh for analysis
        bm = bmesh.new()
        bm.from_mesh(mesh)
        bm.faces.ensure_lookup_table()
        bm.verts.ensure_lookup_table()
        bm.edges.ensure_lookup_table()
        
        mesh_data = {
            "vertex_count": len(bm.verts),
            "edge_count": len(bm.edges),
            "face_count": len(bm.faces),
            "polygon_count": len(mesh.polygons),
            "has_custom_normals": mesh.has_custom_normals,
            "uv_layers": [uv.name for uv in mesh.uv_layers],
            "vertex_colors": [vc.name for vc in mesh.vertex_colors],
            "shape_keys": extract_shape_keys(obj),
            "topology_analysis": analyze_mesh_topology(bm)
        }
        
        bm.free()
        return mesh_data
        
    except Exception as e:
        return {"error": f"Failed to extract mesh data: {str(e)}"}

def extract_shape_keys(obj) -> List[Dict[str, Any]]:
    """Extract shape key information"""
    shape_keys = []
    
    if obj.data.shape_keys:
        for key in obj.data.shape_keys.key_blocks:
            shape_keys.append({
                "name": key.name,
                "value": key.value,
                "slider_min": key.slider_min,
                "slider_max": key.slider_max
            })
    
    return shape_keys

def analyze_mesh_topology(bm) -> Dict[str, Any]:
    """Analyze mesh topology for quality assessment"""
    analysis = {
        "triangles": 0,
        "quads": 0,
        "ngons": 0,
        "non_manifold_edges": 0,
        "loose_vertices": 0,
        "loose_edges": 0
    }
    
    try:
        # Face analysis
        for face in bm.faces:
            if len(face.verts) == 3:
                analysis["triangles"] += 1
            elif len(face.verts) == 4:
                analysis["quads"] += 1
            else:
                analysis["ngons"] += 1
        
        # Edge analysis
        for edge in bm.edges:
            if not edge.is_manifold:
                analysis["non_manifold_edges"] += 1
        
        # Vertex analysis
        for vert in bm.verts:
            if len(vert.link_edges) == 0:
                analysis["loose_vertices"] += 1
        
        # Edge analysis for loose edges
        for edge in bm.edges:
            if len(edge.link_faces) == 0:
                analysis["loose_edges"] += 1
                
    except Exception as e:
        analysis["analysis_error"] = str(e)
    
    return analysis

def extract_material_data(context) -> List[Dict[str, Any]]:
    """Extract material information from scene"""
    materials_data = []
    
    for material in bpy.data.materials:
        if material.users > 0:  # Only include materials that are actually used
            try:
                mat_data = {
                    "name": material.name,
                    "use_nodes": material.use_nodes,
                    "users": material.users,
                    "base_color": list(material.diffuse_color) if hasattr(material, 'diffuse_color') else None,
                    "metallic": getattr(material, 'metallic', None),
                    "roughness": getattr(material, 'roughness', None),
                    "alpha": material.diffuse_color[3] if hasattr(material, 'diffuse_color') and len(material.diffuse_color) > 3 else 1.0
                }
                
                # Add node tree information if using nodes
                if material.use_nodes and material.node_tree:
                    mat_data["node_info"] = {
                        "node_count": len(material.node_tree.nodes),
                        "has_principled_bsdf": any(node.type == 'BSDF_PRINCIPLED' for node in material.node_tree.nodes),
                        "has_textures": any(node.type.startswith('TEX_') for node in material.node_tree.nodes)
                    }
                
                materials_data.append(mat_data)
                
            except Exception as e:
                materials_data.append({
                    "name": material.name,
                    "error": f"Failed to extract material data: {str(e)}"
                })
    
    return materials_data

def extract_lighting_data(context) -> List[Dict[str, Any]]:
    """Extract lighting information from scene"""
    lights_data = []

    for obj in context.scene.objects:
        if obj.type == 'LIGHT' and obj.data:
            try:
                light_data = extract_light_data(obj)
                lights_data.append(light_data)
            except Exception as e:
                lights_data.append({
                    "name": obj.name,
                    "error": f"Failed to extract light data: {str(e)}"
                })

    return lights_data

def extract_light_data(obj) -> Dict[str, Any]:
    """Extract detailed light information"""
    light = obj.data

    light_data = {
        "name": obj.name,
        "type": light.type,
        "energy": light.energy,
        "color": list(light.color),
        "location": list(obj.location),
        "rotation": list(obj.rotation_euler)
    }

    # Add type-specific properties
    if light.type == 'SUN':
        light_data["angle"] = light.angle
    elif light.type == 'SPOT':
        light_data.update({
            "spot_size": light.spot_size,
            "spot_blend": light.spot_blend
        })
    elif light.type == 'AREA':
        light_data.update({
            "shape": light.shape,
            "size": light.size,
            "size_y": getattr(light, 'size_y', light.size)
        })
    elif light.type == 'POINT':
        light_data["shadow_soft_size"] = light.shadow_soft_size

    return light_data

def extract_camera_data(context) -> List[Dict[str, Any]]:
    """Extract camera information from scene"""
    cameras_data = []

    for obj in context.scene.objects:
        if obj.type == 'CAMERA' and obj.data:
            try:
                camera_data = extract_camera_data_from_object(obj)
                cameras_data.append(camera_data)
            except Exception as e:
                cameras_data.append({
                    "name": obj.name,
                    "error": f"Failed to extract camera data: {str(e)}"
                })

    return cameras_data

def extract_camera_data_from_object(obj) -> Dict[str, Any]:
    """Extract detailed camera information"""
    camera = obj.data

    camera_data = {
        "name": obj.name,
        "type": camera.type,
        "lens": camera.lens,
        "location": list(obj.location),
        "rotation": list(obj.rotation_euler),
        "clip_start": camera.clip_start,
        "clip_end": camera.clip_end
    }

    # Add type-specific properties
    if camera.type == 'PERSP':
        camera_data["angle"] = camera.angle
        camera_data["sensor_width"] = camera.sensor_width
        camera_data["sensor_height"] = camera.sensor_height
    elif camera.type == 'ORTHO':
        camera_data["ortho_scale"] = camera.ortho_scale
    elif camera.type == 'PANO':
        camera_data["panorama_type"] = camera.panorama_type

    return camera_data

def extract_world_data(context) -> Dict[str, Any]:
    """Extract world/environment information"""
    world = context.scene.world

    if not world:
        return {"error": "No world data available"}

    world_data = {
        "name": world.name,
        "use_nodes": world.use_nodes,
        "color": list(world.color) if hasattr(world, 'color') else [0.05, 0.05, 0.05]
    }

    # Add node tree information if using nodes
    if world.use_nodes and world.node_tree:
        world_data["node_info"] = {
            "node_count": len(world.node_tree.nodes),
            "has_environment_texture": any(node.type == 'TEX_ENVIRONMENT' for node in world.node_tree.nodes),
            "has_sky_texture": any(node.type == 'TEX_SKY' for node in world.node_tree.nodes)
        }

    return world_data

def extract_scene_properties(context) -> Dict[str, Any]:
    """Extract general scene properties"""
    scene = context.scene

    return {
        "gravity": list(scene.gravity) if hasattr(scene, 'gravity') else [0, 0, -9.81],
        "frame_rate": scene.render.fps,
        "resolution": [scene.render.resolution_x, scene.render.resolution_y],
        "resolution_percentage": scene.render.resolution_percentage,
        "use_motion_blur": scene.render.use_motion_blur,
        "use_freestyle": scene.render.use_freestyle if hasattr(scene.render, 'use_freestyle') else False
    }

def extract_viewport_info(context) -> Dict[str, Any]:
    """Extract current viewport information"""
    viewport_info = {"error": "No 3D viewport found"}

    try:
        # Find 3D viewport
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                space = area.spaces.active
                region_3d = space.region_3d

                viewport_info = {
                    "view_perspective": region_3d.view_perspective,
                    "view_location": list(region_3d.view_location),
                    "view_rotation": list(region_3d.view_rotation),
                    "view_distance": region_3d.view_distance,
                    "shading_type": space.shading.type,
                    "show_overlays": space.overlay.show_overlays,
                    "show_wireframes": space.overlay.show_wireframes,
                    "viewport_shade": space.shading.type
                }
                break

    except Exception as e:
        viewport_info = {"error": f"Failed to extract viewport info: {str(e)}"}

    return viewport_info

def extract_object_hierarchy(context) -> Dict[str, Any]:
    """Extract object hierarchy and relationships"""
    hierarchy = {
        "root_objects": [],
        "parent_child_relationships": [],
        "collection_memberships": {}
    }

    try:
        # Find root objects (objects without parents)
        for obj in context.scene.objects:
            if obj.parent is None:
                hierarchy["root_objects"].append(obj.name)
            else:
                hierarchy["parent_child_relationships"].append({
                    "parent": obj.parent.name,
                    "child": obj.name
                })

        # Collection memberships
        for collection in bpy.data.collections:
            hierarchy["collection_memberships"][collection.name] = [obj.name for obj in collection.objects]

    except Exception as e:
        hierarchy["error"] = f"Failed to extract hierarchy: {str(e)}"

    return hierarchy

# Screenshot Capture Functions

def capture_viewport_screenshot(context, resolution: Tuple[int, int] = None) -> Optional[str]:
    """Capture current viewport as base64 encoded image"""
    if not PIL_AVAILABLE:
        raise VisionSystemError("PIL (Pillow) is required for screenshot capture")

    if resolution is None:
        resolution = VISION_CONFIG["default_screenshot_resolution"]

    try:
        # Get current 3D viewport
        area = None
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                break

        if not area:
            raise VisionSystemError("No 3D viewport found")

        # Get the region
        region = None
        for region in area.regions:
            if region.type == 'WINDOW':
                break

        if not region:
            raise VisionSystemError("No viewport region found")

        # Use Blender's built-in screenshot capability
        return capture_viewport_with_blender_api(context, area, region, resolution)

    except Exception as e:
        print(f"Error capturing screenshot: {e}")
        return None

def capture_viewport_with_blender_api(context, area, region, resolution: Tuple[int, int]) -> str:
    """Capture viewport using Blender's API"""
    try:
        # Store original settings
        space = area.spaces.active
        original_shading = space.shading.type

        # Temporarily set to material preview for better AI analysis
        space.shading.type = 'MATERIAL_PREVIEW'

        # Force viewport update
        bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

        # Create a temporary image for capture
        temp_image_name = "BLENDPRO_TEMP_SCREENSHOT"

        # Remove existing temp image if it exists
        if temp_image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[temp_image_name])

        # Create new image
        temp_image = bpy.data.images.new(temp_image_name, width=resolution[0], height=resolution[1])

        # Capture the viewport
        with context.temp_override(area=area, region=region):
            # Use screen capture operator
            bpy.ops.screen.screenshot(filepath="/tmp/blendpro_temp.png", full=False)

        # Load the captured image
        try:
            from PIL import Image
            pil_image = Image.open("/tmp/blendpro_temp.png")

            # Resize if needed
            if pil_image.size != resolution:
                pil_image = pil_image.resize(resolution, Image.Resampling.LANCZOS)

            # Convert to RGB if needed
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            # Encode to base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG', quality=VISION_CONFIG["screenshot_quality"])
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Cleanup
            pil_image.close()
            buffer.close()

            # Remove temp files
            try:
                os.remove("/tmp/blendpro_temp.png")
            except:
                pass

        except Exception as e:
            print(f"Error processing captured image: {e}")
            # Fallback: create a simple colored image
            image_base64 = create_fallback_screenshot(resolution)

        # Restore original shading
        space.shading.type = original_shading

        # Remove temp image from Blender
        if temp_image_name in bpy.data.images:
            bpy.data.images.remove(bpy.data.images[temp_image_name])

        return image_base64

    except Exception as e:
        raise VisionSystemError(f"Failed to capture viewport: {str(e)}")

def create_fallback_screenshot(resolution: Tuple[int, int]) -> str:
    """Create a fallback screenshot when capture fails"""
    try:
        from PIL import Image, ImageDraw, ImageFont

        # Create a simple image with text
        image = Image.new('RGB', resolution, color=(64, 64, 64))
        draw = ImageDraw.Draw(image)

        # Add text
        text = "BlendPro Vision\nScreenshot Capture\nFallback Mode"

        # Try to use a font, fallback to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()

        # Calculate text position (center)
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]

        x = (resolution[0] - text_width) // 2
        y = (resolution[1] - text_height) // 2

        draw.text((x, y), text, fill=(255, 255, 255), font=font)

        # Encode to base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

        image.close()
        buffer.close()

        return image_base64

    except Exception as e:
        print(f"Error creating fallback screenshot: {e}")
        return ""

def capture_multi_view_screenshots(context) -> Dict[str, str]:
    """Capture screenshots from multiple predefined viewpoints"""
    if not VISION_CONFIG["enable_multi_view"]:
        # Return single screenshot if multi-view is disabled
        single_shot = capture_viewport_screenshot(context)
        return {"main": single_shot} if single_shot else {}

    screenshots = {}

    # Store original view settings
    area = None
    for area in context.screen.areas:
        if area.type == 'VIEW_3D':
            break

    if not area:
        return screenshots

    space = area.spaces.active
    region_3d = space.region_3d

    # Store original view matrix and settings
    original_view_matrix = region_3d.view_matrix.copy()
    original_view_distance = region_3d.view_distance
    original_view_location = region_3d.view_location.copy()
    original_view_rotation = region_3d.view_rotation.copy()

    # Define viewpoints
    viewpoints = {
        "perspective": {
            "view_rotation": mathutils.Quaternion((0.7071, 0.7071, 0.0, 0.0)),
            "description": "Default perspective view"
        },
        "front": {
            "view_rotation": mathutils.Quaternion((1.0, 0.0, 0.0, 0.0)),
            "description": "Front orthographic view"
        },
        "right": {
            "view_rotation": mathutils.Quaternion((0.7071, 0.0, 0.0, 0.7071)),
            "description": "Right side orthographic view"
        },
        "top": {
            "view_rotation": mathutils.Quaternion((0.0, 0.0, 0.0, 1.0)),
            "description": "Top orthographic view"
        }
    }

    try:
        for view_name, view_data in viewpoints.items():
            try:
                # Set the view
                region_3d.view_rotation = view_data["view_rotation"]

                # Frame all objects for better view
                with context.temp_override(area=area):
                    bpy.ops.view3d.view_all()

                # Force viewport update
                bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

                # Small delay to ensure view is updated
                time.sleep(0.1)

                # Capture screenshot
                screenshot = capture_viewport_screenshot(context)
                if screenshot:
                    screenshots[view_name] = screenshot

            except Exception as e:
                print(f"Error capturing {view_name} view: {e}")
                continue

    except Exception as e:
        print(f"Error in multi-view capture: {e}")

    finally:
        # Restore original view
        try:
            region_3d.view_matrix = original_view_matrix
            region_3d.view_distance = original_view_distance
            region_3d.view_location = original_view_location
            region_3d.view_rotation = original_view_rotation
        except Exception as e:
            print(f"Error restoring view: {e}")

    return screenshots

# Vision API Integration Functions

def analyze_scene_with_vision(context, user_prompt: str, api_key: str, base_url: str = None, model: str = None) -> Dict[str, Any]:
    """Perform multi-modal scene analysis using vision-capable AI models"""

    if model is None:
        model = VISION_CONFIG["vision_model_default"]

    print(f"Using vision model: {model}")
    print(f"Using vision API base URL: {base_url if base_url else 'default'}")

    try:
        # Import OpenAI here to avoid import issues if not available
        from openai import OpenAI

        # Extract scene data
        print("Extracting scene data...")
        scene_data = extract_comprehensive_scene_data(context)

        # Capture screenshots
        print("Capturing screenshots...")
        screenshots = capture_multi_view_screenshots(context)

        if not screenshots:
            # Fallback to single screenshot
            single_screenshot = capture_viewport_screenshot(context)
            if single_screenshot:
                screenshots = {"main": single_screenshot}

        if not screenshots:
            return {"error": "Failed to capture any screenshots", "code": None, "analysis": None}

        print(f"Captured {len(screenshots)} screenshots")

        # Create OpenAI client
        client_kwargs = {"api_key": api_key, "timeout": 60}
        if base_url and base_url.strip():
            client_kwargs["base_url"] = base_url

        client = OpenAI(**client_kwargs)

        # Prepare multi-modal messages
        messages = create_vision_messages(screenshots, scene_data, user_prompt)

        print(f"Making vision API call with model: {model}")

        # Make API call
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=2000,
            temperature=0.7
        )

        if response.choices and response.choices[0].message:
            content = response.choices[0].message.content

            # Parse response for code and annotations
            parsed_response = parse_vision_response(content, scene_data)

            return {
                "error": None,
                "code": parsed_response.get("code"),
                "analysis": parsed_response.get("analysis"),
                "annotations": parsed_response.get("annotations"),
                "full_response": content,
                "scene_data": scene_data,
                "screenshots_count": len(screenshots)
            }
        else:
            return {"error": "No response from vision API", "code": None, "analysis": None}

    except Exception as e:
        return {"error": f"Vision analysis failed: {str(e)}", "code": None, "analysis": None}

def create_vision_messages(screenshots: Dict[str, str], scene_data: Dict[str, Any], user_prompt: str) -> List[Dict[str, Any]]:
    """Create multi-modal messages for vision API"""

    messages = [
        {
            "role": "system",
            "content": """You are a professional 3D artist assistant with advanced understanding of Blender.
You can see the 3D scene from multiple angles and have access to detailed technical scene data.

Your capabilities include:
- Analyzing 3D composition and spatial relationships
- Understanding lighting setups and material properties
- Recognizing modeling techniques and topology
- Providing contextual, spatial-aware responses
- Generating precise Blender Python code based on visual analysis

When responding:
1. Reference specific objects you can see in the images
2. Consider the technical data provided
3. Provide actionable Blender Python code when requested
4. Explain your visual observations when relevant

Available visual annotation commands (use when helpful):
- [HIGHLIGHT:object_name] - Highlight specific object
- [POINT_TO:x,y,z] - Point to specific coordinates
- [CIRCLE:object_name] - Draw circle around object
- [ARROW:from_obj,to_obj] - Draw arrow between objects

Always wrap your Python code in triple backticks with 'python' language specification."""
        }
    ]

    # Add screenshots to messages
    for view_name, screenshot_data in screenshots.items():
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": f"Scene view from {view_name} angle:"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{screenshot_data}",
                        "detail": "high"
                    }
                }
            ]
        })

    # Add technical scene data
    scene_summary = create_scene_summary_for_ai(scene_data)
    messages.append({
        "role": "user",
        "content": f"""Technical Scene Data Summary:

{scene_summary}

User Request: {user_prompt}

Please analyze the scene visually and technically, then provide a response that takes into account both the visual elements you can see and the technical data provided. If generating code, make it specific to the objects and setup visible in the scene."""
    })

    return messages

def create_scene_summary_for_ai(scene_data: Dict[str, Any]) -> str:
    """Create a concise summary of scene data for AI analysis"""

    objects = scene_data.get("objects", [])
    materials = scene_data.get("materials", [])
    lights = scene_data.get("lights", [])
    cameras = scene_data.get("cameras", [])
    metadata = scene_data.get("metadata", {})

    summary = f"""Scene Overview:
- Objects: {len(objects)} total
- Materials: {len(materials)} total
- Lights: {len(lights)} total
- Cameras: {len(cameras)} total
- Render Engine: {metadata.get('render_engine', 'Unknown')}
- Blender Version: {metadata.get('blender_version', 'Unknown')}

Object Details:"""

    # Add object information (limit to prevent token overflow)
    max_objects = VISION_CONFIG["max_objects_in_summary"]
    for i, obj in enumerate(objects[:max_objects]):
        obj_type = obj.get("type", "Unknown")
        obj_name = obj.get("name", "Unnamed")
        location = obj.get("location", [0, 0, 0])

        summary += f"\n- {obj_name} ({obj_type}) at {location}"

        # Add material info if available
        materials_list = obj.get("material_slots", [])
        if materials_list:
            mat_names = [slot.get("name", "Unknown") for slot in materials_list]
            summary += f" | Materials: {', '.join(mat_names)}"

        # Add modifier info if available
        modifiers_list = obj.get("modifiers", [])
        if modifiers_list:
            mod_names = [mod.get("name", "Unknown") for mod in modifiers_list]
            summary += f" | Modifiers: {', '.join(mod_names)}"

        # Add mesh info for mesh objects
        if obj_type == 'MESH' and obj.get("mesh_data"):
            mesh_data = obj["mesh_data"]
            if not mesh_data.get("error"):
                summary += f" | Verts: {mesh_data.get('vertex_count', 0)}, Faces: {mesh_data.get('face_count', 0)}"

    if len(objects) > max_objects:
        summary += f"\n... and {len(objects) - max_objects} more objects"

    # Add lighting information
    if lights:
        summary += f"\n\nLighting Setup:"
        for light in lights:
            if not light.get("error"):
                light_name = light.get("name", "Unknown")
                light_type = light.get("type", "Unknown")
                energy = light.get("energy", "Unknown")
                summary += f"\n- {light_name} ({light_type}) | Energy: {energy}"

    # Add camera information
    if cameras:
        summary += f"\n\nCameras:"
        for camera in cameras:
            if not camera.get("error"):
                cam_name = camera.get("name", "Unknown")
                cam_type = camera.get("type", "Unknown")
                lens = camera.get("lens", "Unknown")
                summary += f"\n- {cam_name} ({cam_type}) | Lens: {lens}mm"

    return summary

def parse_vision_response(response_content: str, scene_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse AI response for code, analysis, and visual annotations"""

    parsed = {
        "code": None,
        "analysis": "",
        "annotations": []
    }

    # Extract code blocks
    code_matches = re.findall(r'```(?:python)?\s*(.*?)```', response_content, re.DOTALL)
    if code_matches:
        # Get the first code block and clean it
        parsed["code"] = code_matches[0].strip()

    # Extract visual annotations
    annotations = []

    # Highlight commands
    highlight_matches = re.findall(r'\[HIGHLIGHT:(\w+)\]', response_content)
    for obj_name in highlight_matches:
        annotations.append({
            "type": "highlight",
            "target": obj_name,
            "color": (1, 1, 0, 0.7)  # Yellow highlight
        })

    # Point-to commands
    point_matches = re.findall(r'\[POINT_TO:([\d\.\-,\s]+)\]', response_content)
    for coords_str in point_matches:
        try:
            coords = [float(x.strip()) for x in coords_str.split(',')]
            if len(coords) == 3:
                annotations.append({
                    "type": "point",
                    "location": coords,
                    "color": (0, 1, 0, 1)  # Green point
                })
        except ValueError:
            continue

    # Circle commands
    circle_matches = re.findall(r'\[CIRCLE:(\w+)\]', response_content)
    for obj_name in circle_matches:
        annotations.append({
            "type": "circle",
            "target": obj_name,
            "color": (0, 0, 1, 0.8)  # Blue circle
        })

    # Arrow commands
    arrow_matches = re.findall(r'\[ARROW:(\w+),(\w+)\]', response_content)
    for from_obj, to_obj in arrow_matches:
        annotations.append({
            "type": "arrow",
            "from_object": from_obj,
            "to_object": to_obj,
            "color": (1, 0, 1, 0.9)  # Magenta arrow
        })

    parsed["annotations"] = annotations

    # Extract analysis text (everything that's not code or annotations)
    analysis_text = response_content

    # Remove code blocks
    analysis_text = re.sub(r'```.*?```', '', analysis_text, flags=re.DOTALL)

    # Remove annotation commands
    analysis_text = re.sub(r'\[(?:HIGHLIGHT|POINT_TO|CIRCLE|ARROW):[^\]]+\]', '', analysis_text)

    parsed["analysis"] = analysis_text.strip()

    return parsed

# Utility Functions

def create_vision_context_for_base_ai(vision_result: Dict[str, Any]) -> str:
    """Create context string from vision analysis to pass to base AI"""

    if vision_result.get("error"):
        return f"Vision analysis failed: {vision_result['error']}"

    context_parts = []

    # Add analysis if available
    if vision_result.get("analysis"):
        context_parts.append(f"Visual Analysis: {vision_result['analysis']}")

    # Add scene summary
    if vision_result.get("scene_data"):
        scene_summary = create_scene_summary_for_ai(vision_result["scene_data"])
        context_parts.append(f"Scene Data: {scene_summary}")

    # Add screenshot info
    if vision_result.get("screenshots_count"):
        context_parts.append(f"Screenshots captured: {vision_result['screenshots_count']} views")

    # Add annotations info
    if vision_result.get("annotations"):
        annotations_summary = f"Visual annotations available: {len(vision_result['annotations'])} items"
        context_parts.append(annotations_summary)

    return "\n\n".join(context_parts)

def test_vision_system(context) -> Dict[str, Any]:
    """Test vision system functionality"""

    test_results = {
        "dependencies": check_vision_dependencies(),
        "status": get_vision_system_status(),
        "tests": {}
    }

    # Test scene data extraction
    try:
        scene_data = extract_comprehensive_scene_data(context)
        test_results["tests"]["scene_data_extraction"] = {
            "success": True,
            "objects_count": len(scene_data.get("objects", [])),
            "materials_count": len(scene_data.get("materials", [])),
            "lights_count": len(scene_data.get("lights", []))
        }
    except Exception as e:
        test_results["tests"]["scene_data_extraction"] = {
            "success": False,
            "error": str(e)
        }

    # Test screenshot capture
    try:
        screenshot = capture_viewport_screenshot(context)
        test_results["tests"]["screenshot_capture"] = {
            "success": screenshot is not None,
            "has_data": len(screenshot) > 0 if screenshot else False
        }
    except Exception as e:
        test_results["tests"]["screenshot_capture"] = {
            "success": False,
            "error": str(e)
        }

    return test_results
