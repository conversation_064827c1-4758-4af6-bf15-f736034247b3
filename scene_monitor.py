"""
BlendPro Real-Time Scene Monitoring System
Continuous scene analysis with proactive suggestions

Author: inkbytefo
"""

import bpy
import bmesh
import hashlib
import time
import threading
import json
from typing import Dict, List, Any, Optional, Callable
from collections import deque

# Import vision utilities
try:
    from .vision_utilities import extract_comprehensive_scene_data, get_vision_system_status
    VISION_AVAILABLE = True
except ImportError:
    VISION_AVAILABLE = False

class SceneChangeDetector:
    """Detects changes in the Blender scene"""
    
    def __init__(self):
        self.last_scene_hash = None
        self.last_object_count = 0
        self.last_material_count = 0
        self.last_light_count = 0
        self.change_threshold = 0.1
        self.detailed_tracking = True
        
    def calculate_scene_hash(self, context) -> str:
        """Calculate a hash representing the current scene state"""
        try:
            scene_signature = []
            
            # Basic scene info
            scene = context.scene
            scene_signature.append(f"frame:{scene.frame_current}")
            scene_signature.append(f"engine:{scene.render.engine}")
            
            # Object signatures
            for obj in context.scene.objects:
                if obj.visible_get():  # Only track visible objects
                    obj_sig = (
                        f"{obj.name}_{obj.type}_"
                        f"{obj.location.x:.2f}_{obj.location.y:.2f}_{obj.location.z:.2f}_"
                        f"{obj.rotation_euler.x:.2f}_{obj.rotation_euler.y:.2f}_{obj.rotation_euler.z:.2f}_"
                        f"{obj.scale.x:.2f}_{obj.scale.y:.2f}_{obj.scale.z:.2f}_"
                        f"{len(obj.material_slots)}_{len(obj.modifiers)}"
                    )
                    scene_signature.append(obj_sig)
            
            # Material signatures
            for material in bpy.data.materials:
                if material.users > 0:
                    mat_sig = f"mat_{material.name}_{material.use_nodes}"
                    scene_signature.append(mat_sig)
            
            # Light signatures
            for obj in context.scene.objects:
                if obj.type == 'LIGHT':
                    light_sig = f"light_{obj.name}_{obj.data.energy}_{obj.data.type}"
                    scene_signature.append(light_sig)
            
            # Create hash
            combined_signature = "|".join(sorted(scene_signature))
            return hashlib.md5(combined_signature.encode()).hexdigest()
            
        except Exception as e:
            print(f"Error calculating scene hash: {e}")
            return str(time.time())  # Fallback to timestamp
    
    def detect_changes(self, context) -> Dict[str, Any]:
        """Detect what has changed in the scene"""
        current_hash = self.calculate_scene_hash(context)
        
        changes = {
            "has_changes": False,
            "hash_changed": False,
            "object_count_changed": False,
            "material_count_changed": False,
            "light_count_changed": False,
            "change_details": {}
        }
        
        # Check hash change
        if self.last_scene_hash and current_hash != self.last_scene_hash:
            changes["has_changes"] = True
            changes["hash_changed"] = True
        
        # Check object count
        current_object_count = len([obj for obj in context.scene.objects if obj.visible_get()])
        if self.last_object_count != current_object_count:
            changes["has_changes"] = True
            changes["object_count_changed"] = True
            changes["change_details"]["objects"] = {
                "previous": self.last_object_count,
                "current": current_object_count,
                "difference": current_object_count - self.last_object_count
            }
        
        # Check material count
        current_material_count = len([mat for mat in bpy.data.materials if mat.users > 0])
        if self.last_material_count != current_material_count:
            changes["has_changes"] = True
            changes["material_count_changed"] = True
            changes["change_details"]["materials"] = {
                "previous": self.last_material_count,
                "current": current_material_count,
                "difference": current_material_count - self.last_material_count
            }
        
        # Check light count
        current_light_count = len([obj for obj in context.scene.objects if obj.type == 'LIGHT'])
        if self.last_light_count != current_light_count:
            changes["has_changes"] = True
            changes["light_count_changed"] = True
            changes["change_details"]["lights"] = {
                "previous": self.last_light_count,
                "current": current_light_count,
                "difference": current_light_count - self.last_light_count
            }
        
        # Update stored values
        self.last_scene_hash = current_hash
        self.last_object_count = current_object_count
        self.last_material_count = current_material_count
        self.last_light_count = current_light_count
        
        return changes

class SceneHealthAnalyzer:
    """Analyzes scene health and provides suggestions"""
    
    def __init__(self):
        self.analysis_rules = {
            "lighting": self._analyze_lighting,
            "materials": self._analyze_materials,
            "topology": self._analyze_topology,
            "performance": self._analyze_performance,
            "composition": self._analyze_composition
        }
    
    def analyze_scene_health(self, context) -> Dict[str, Any]:
        """Perform comprehensive scene health analysis"""
        if not VISION_AVAILABLE:
            return {"error": "Vision system not available for scene analysis"}
        
        try:
            # Extract scene data
            scene_data = extract_comprehensive_scene_data(context)
            
            health_report = {
                "overall_score": 0,
                "issues": [],
                "suggestions": [],
                "warnings": [],
                "analysis_details": {}
            }
            
            # Run all analysis rules
            total_score = 0
            analysis_count = 0
            
            for analysis_name, analysis_func in self.analysis_rules.items():
                try:
                    result = analysis_func(scene_data, context)
                    health_report["analysis_details"][analysis_name] = result
                    
                    if "score" in result:
                        total_score += result["score"]
                        analysis_count += 1
                    
                    if "issues" in result:
                        health_report["issues"].extend(result["issues"])
                    
                    if "suggestions" in result:
                        health_report["suggestions"].extend(result["suggestions"])
                    
                    if "warnings" in result:
                        health_report["warnings"].extend(result["warnings"])
                        
                except Exception as e:
                    health_report["warnings"].append(f"Analysis '{analysis_name}' failed: {str(e)}")
            
            # Calculate overall score
            if analysis_count > 0:
                health_report["overall_score"] = total_score / analysis_count
            
            return health_report
            
        except Exception as e:
            return {"error": f"Scene health analysis failed: {str(e)}"}
    
    def _analyze_lighting(self, scene_data: Dict[str, Any], context) -> Dict[str, Any]:
        """Analyze lighting setup"""
        lights = scene_data.get("lights", [])
        
        analysis = {
            "score": 50,  # Default neutral score
            "issues": [],
            "suggestions": [],
            "warnings": []
        }
        
        light_count = len([light for light in lights if not light.get("error")])
        
        if light_count == 0:
            analysis["score"] = 20
            analysis["issues"].append("No lights in scene - objects may appear flat in renders")
            analysis["suggestions"].append("Add at least one light source (Sun, Point, or Area light)")
        elif light_count == 1:
            analysis["score"] = 60
            analysis["suggestions"].append("Consider adding a second light for better illumination (three-point lighting)")
        elif light_count >= 2 and light_count <= 4:
            analysis["score"] = 90
        else:
            analysis["score"] = 70
            analysis["warnings"].append(f"Many lights ({light_count}) may impact performance")
        
        # Check for three-point lighting setup
        if light_count >= 3:
            analysis["suggestions"].append("You have a good foundation for three-point lighting setup")
        
        return analysis
    
    def _analyze_materials(self, scene_data: Dict[str, Any], context) -> Dict[str, Any]:
        """Analyze material usage"""
        objects = scene_data.get("objects", [])
        materials = scene_data.get("materials", [])
        
        analysis = {
            "score": 50,
            "issues": [],
            "suggestions": [],
            "warnings": []
        }
        
        # Find objects without materials
        mesh_objects = [obj for obj in objects if obj.get("type") == "MESH" and not obj.get("error")]
        objects_without_materials = []
        
        for obj in mesh_objects:
            material_slots = obj.get("material_slots", [])
            if not material_slots or not any(slot.get("name") for slot in material_slots):
                objects_without_materials.append(obj.get("name", "Unknown"))
        
        if objects_without_materials:
            analysis["score"] = max(20, analysis["score"] - len(objects_without_materials) * 10)
            analysis["issues"].append(f"Objects without materials: {', '.join(objects_without_materials[:5])}")
            analysis["suggestions"].append("Add materials to improve visual quality")
        
        # Check material complexity
        node_based_materials = len([mat for mat in materials if mat.get("use_nodes", False)])
        if node_based_materials > 0:
            analysis["score"] = min(100, analysis["score"] + 20)
            analysis["suggestions"].append("Good use of node-based materials for realistic rendering")
        
        return analysis
    
    def _analyze_topology(self, scene_data: Dict[str, Any], context) -> Dict[str, Any]:
        """Analyze mesh topology"""
        objects = scene_data.get("objects", [])
        
        analysis = {
            "score": 80,
            "issues": [],
            "suggestions": [],
            "warnings": []
        }
        
        topology_issues = []
        
        for obj in objects:
            if obj.get("type") == "MESH" and obj.get("mesh_data"):
                mesh_data = obj["mesh_data"]
                if not mesh_data.get("error"):
                    topology = mesh_data.get("topology_analysis", {})
                    
                    # Check for topology issues
                    if topology.get("non_manifold_edges", 0) > 0:
                        topology_issues.append(f"{obj['name']}: {topology['non_manifold_edges']} non-manifold edges")
                    
                    if topology.get("loose_vertices", 0) > 0:
                        topology_issues.append(f"{obj['name']}: {topology['loose_vertices']} loose vertices")
                    
                    if topology.get("ngons", 0) > topology.get("quads", 0) + topology.get("triangles", 0):
                        topology_issues.append(f"{obj['name']}: High ngon count may cause issues")
        
        if topology_issues:
            analysis["score"] = max(30, analysis["score"] - len(topology_issues) * 15)
            analysis["issues"].extend(topology_issues[:3])  # Limit to first 3 issues
            analysis["suggestions"].append("Clean up mesh topology for better performance and rendering")
        
        return analysis

    def _analyze_performance(self, scene_data: Dict[str, Any], context) -> Dict[str, Any]:
        """Analyze scene performance characteristics"""
        objects = scene_data.get("objects", [])

        analysis = {
            "score": 80,
            "issues": [],
            "suggestions": [],
            "warnings": []
        }

        total_vertices = 0
        total_faces = 0
        high_poly_objects = []
        objects_with_modifiers = []

        # Analyze mesh complexity
        for obj in objects:
            if obj.get("type") == "MESH" and obj.get("mesh_data"):
                mesh_data = obj["mesh_data"]
                if not mesh_data.get("error"):
                    vertex_count = mesh_data.get("vertex_count", 0)
                    face_count = mesh_data.get("face_count", 0)

                    total_vertices += vertex_count
                    total_faces += face_count

                    # Check for high-poly objects
                    if vertex_count > 10000:
                        high_poly_objects.append(f"{obj['name']} ({vertex_count:,} verts)")

                    # Check for objects with many modifiers
                    modifiers = obj.get("modifiers", [])
                    if len(modifiers) > 3:
                        objects_with_modifiers.append(f"{obj['name']} ({len(modifiers)} modifiers)")

        # Performance scoring based on complexity
        if total_vertices > 1000000:  # 1M+ vertices
            analysis["score"] = max(20, analysis["score"] - 40)
            analysis["warnings"].append(f"Very high vertex count: {total_vertices:,} total vertices")
        elif total_vertices > 500000:  # 500K+ vertices
            analysis["score"] = max(40, analysis["score"] - 20)
            analysis["warnings"].append(f"High vertex count: {total_vertices:,} total vertices")

        if total_faces > 500000:  # 500K+ faces
            analysis["score"] = max(30, analysis["score"] - 30)
            analysis["warnings"].append(f"Very high face count: {total_faces:,} total faces")

        # High-poly object warnings
        if high_poly_objects:
            analysis["score"] = max(50, analysis["score"] - len(high_poly_objects) * 10)
            analysis["issues"].extend([f"High-poly object: {obj}" for obj in high_poly_objects[:3]])
            analysis["suggestions"].append("Consider using LOD (Level of Detail) for high-poly objects")

        # Modifier complexity warnings
        if objects_with_modifiers:
            analysis["score"] = max(60, analysis["score"] - len(objects_with_modifiers) * 5)
            analysis["warnings"].extend([f"Complex modifier stack: {obj}" for obj in objects_with_modifiers[:2]])
            analysis["suggestions"].append("Consider applying modifiers if not needed for animation")

        # Check render engine performance implications
        render_engine = scene_data.get("metadata", {}).get("render_engine", "")
        if render_engine == "CYCLES":
            if total_faces > 100000:
                analysis["suggestions"].append("Consider using Cycles adaptive subdivision for complex meshes")
        elif render_engine == "EEVEE":
            if total_faces > 200000:
                analysis["suggestions"].append("EEVEE performs better with moderate polygon counts")

        # Memory usage estimation
        estimated_memory_mb = (total_vertices * 32 + total_faces * 16) / (1024 * 1024)  # Rough estimate
        if estimated_memory_mb > 500:  # 500MB+
            analysis["warnings"].append(f"Estimated memory usage: {estimated_memory_mb:.1f}MB")

        return analysis

    def _analyze_composition(self, scene_data: Dict[str, Any], context) -> Dict[str, Any]:
        """Analyze scene composition and layout"""
        objects = scene_data.get("objects", [])
        cameras = scene_data.get("cameras", [])

        analysis = {
            "score": 70,
            "issues": [],
            "suggestions": [],
            "warnings": []
        }

        # Check camera setup
        if not cameras:
            analysis["score"] = max(30, analysis["score"] - 30)
            analysis["issues"].append("No cameras in scene")
            analysis["suggestions"].append("Add a camera for proper composition and rendering")
        elif len(cameras) == 1:
            analysis["score"] = min(100, analysis["score"] + 10)
            analysis["suggestions"].append("Good camera setup for rendering")

        # Check object distribution
        mesh_objects = [obj for obj in objects if obj.get("type") == "MESH" and not obj.get("error")]
        if mesh_objects:
            # Calculate scene bounds
            min_coords = [float('inf')] * 3
            max_coords = [float('-inf')] * 3

            for obj in mesh_objects:
                location = obj.get("location", [0, 0, 0])
                dimensions = obj.get("dimensions", [0, 0, 0])

                for i in range(3):
                    min_coords[i] = min(min_coords[i], location[i] - dimensions[i]/2)
                    max_coords[i] = max(max_coords[i], location[i] + dimensions[i]/2)

            # Check if objects are too spread out
            scene_size = max(max_coords[i] - min_coords[i] for i in range(3))
            if scene_size > 100:
                analysis["warnings"].append(f"Objects spread over large area ({scene_size:.1f} units)")
                analysis["suggestions"].append("Consider grouping objects closer together for better composition")

            # Check for objects at origin
            objects_at_origin = [obj for obj in mesh_objects
                               if all(abs(obj.get("location", [0,0,0])[i]) < 0.01 for i in range(3))]
            if len(objects_at_origin) > 3:
                analysis["warnings"].append(f"{len(objects_at_origin)} objects at origin - may cause overlap")
                analysis["suggestions"].append("Spread objects out to avoid overlapping at origin")

        # Check for empty scene
        if len(mesh_objects) == 0:
            analysis["score"] = 20
            analysis["issues"].append("No mesh objects in scene")
            analysis["suggestions"].append("Add some objects to create your scene")
        elif len(mesh_objects) == 1:
            analysis["suggestions"].append("Consider adding more objects for a richer composition")

        return analysis

class RealTimeSceneMonitor:
    """Main real-time scene monitoring system"""

    def __init__(self):
        self.change_detector = SceneChangeDetector()
        self.health_analyzer = SceneHealthAnalyzer()
        self.monitoring_active = False
        self.monitoring_thread = None
        self.monitoring_interval = 2.0  # seconds
        self.change_callbacks = []
        self.suggestion_queue = deque(maxlen=10)
        self.last_analysis_time = 0
        self.analysis_cooldown = 10.0  # seconds between full analyses

    def start_monitoring(self, context):
        """Start real-time scene monitoring"""
        if self.monitoring_active:
            return False

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(context,),
            daemon=True
        )
        self.monitoring_thread.start()
        return True

    def stop_monitoring(self):
        """Stop real-time scene monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        return True

    def add_change_callback(self, callback: Callable):
        """Add a callback function to be called when changes are detected"""
        self.change_callbacks.append(callback)

    def remove_change_callback(self, callback: Callable):
        """Remove a change callback"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)

    def get_recent_suggestions(self) -> List[str]:
        """Get recent suggestions from the queue"""
        return list(self.suggestion_queue)

    def _monitoring_loop(self, context):
        """Main monitoring loop (runs in background thread)"""
        while self.monitoring_active:
            try:
                # Detect changes
                changes = self.change_detector.detect_changes(context)

                if changes["has_changes"]:
                    # Notify callbacks
                    for callback in self.change_callbacks:
                        try:
                            callback(changes)
                        except Exception as e:
                            print(f"Error in change callback: {e}")

                    # Perform health analysis if enough time has passed
                    current_time = time.time()
                    if current_time - self.last_analysis_time > self.analysis_cooldown:
                        self._perform_proactive_analysis(context, changes)
                        self.last_analysis_time = current_time

                # Sleep until next check
                time.sleep(self.monitoring_interval)

            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)

    def _perform_proactive_analysis(self, context, changes):
        """Perform proactive analysis and generate suggestions"""
        try:
            health_report = self.health_analyzer.analyze_scene_health(context)

            if health_report.get("error"):
                return

            # Generate contextual suggestions based on changes
            suggestions = []

            # Add change-specific suggestions
            if changes.get("object_count_changed"):
                change_details = changes.get("change_details", {}).get("objects", {})
                if change_details.get("difference", 0) > 0:
                    suggestions.append("New objects added - consider adding materials and proper lighting")

            if changes.get("light_count_changed"):
                change_details = changes.get("change_details", {}).get("lights", {})
                if change_details.get("difference", 0) > 0:
                    suggestions.append("New lights added - check for proper three-point lighting setup")

            if changes.get("material_count_changed"):
                change_details = changes.get("change_details", {}).get("materials", {})
                if change_details.get("difference", 0) > 0:
                    suggestions.append("New materials added - ensure consistent material quality")

            # Add health-based suggestions
            health_suggestions = health_report.get("suggestions", [])
            suggestions.extend(health_suggestions[:2])  # Limit to top 2 suggestions

            # Add to suggestion queue
            for suggestion in suggestions:
                if suggestion not in self.suggestion_queue:
                    self.suggestion_queue.append(suggestion)

        except Exception as e:
            print(f"Error in proactive analysis: {e}")

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            "active": self.monitoring_active,
            "interval": self.monitoring_interval,
            "suggestion_count": len(self.suggestion_queue),
            "last_analysis": self.last_analysis_time,
            "thread_alive": self.monitoring_thread.is_alive() if self.monitoring_thread else False
        }

    def set_monitoring_interval(self, interval: float):
        """Set monitoring interval in seconds"""
        self.monitoring_interval = max(0.5, min(10.0, interval))

    def set_analysis_cooldown(self, cooldown: float):
        """Set analysis cooldown in seconds"""
        self.analysis_cooldown = max(5.0, min(60.0, cooldown))

# Global monitor instance
_global_monitor = None

def get_scene_monitor() -> RealTimeSceneMonitor:
    """Get the global scene monitor instance"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = RealTimeSceneMonitor()
    return _global_monitor

def start_scene_monitoring(context) -> bool:
    """Start scene monitoring"""
    monitor = get_scene_monitor()
    return monitor.start_monitoring(context)

def stop_scene_monitoring() -> bool:
    """Stop scene monitoring"""
    monitor = get_scene_monitor()
    return monitor.stop_monitoring()

def get_monitoring_status() -> Dict[str, Any]:
    """Get monitoring status"""
    monitor = get_scene_monitor()
    return monitor.get_monitoring_status()

def get_recent_suggestions() -> List[str]:
    """Get recent suggestions"""
    monitor = get_scene_monitor()
    return monitor.get_recent_suggestions()

def analyze_scene_health_now(context) -> Dict[str, Any]:
    """Perform immediate scene health analysis"""
    analyzer = SceneHealthAnalyzer()
    return analyzer.analyze_scene_health(context)

# Configuration
MONITOR_CONFIG = {
    "default_interval": 2.0,
    "default_cooldown": 10.0,
    "max_suggestions": 10,
    "enable_proactive_analysis": True,
    "enable_change_detection": True
}
