# BlendPro AI İyileştirmeleri

Bu dokümantasyon, BlendPro Blender eklentisine eklenen yeni yapay zeka özelliklerini açıklar.

## 🚀 Yeni Özellikler

### 1. Çok Adımlı Görev Planlama (Multi-Step Task Planning)

BlendPro artık karmaşık komutları otomatik olarak algılayıp küçük adımlara bölebilir.

#### Nasıl Çalışır:
- Karmaşık komutlar otomatik olarak tespit edilir
- AI komutu mantıklı adımlara böler
- Her adım sırayla çalıştırılır
- Başarısız adımlar raporlanır
- Tüm adımlar tek bir kod bloğunda birleştirilir

#### Örnek Kullanım:
```
Komut: "Alçak tavanlı, ahşap dokulu, içinde bir masa ve sandalye olan basit bir oda yap"

AI Planlaması:
1. Oda için küp oluştur ve ölçeklendir
2. Ahşap materyal oluştur  
3. Ahşap materyali duvarlara ata
4. Masa oluştur
5. Sandalye oluştur
6. Nesneleri odanın içine yerleştir
```

#### Desteklenen Karmaşık Görev Türleri:
- Çoklu nesne oluşturma ("masa ve sandalye oluştur")
- Materyal + nesne kombinasyonları ("ahşap masa oluştur")
- Oda/sahne oluşturma ("oturma odası yap")
- Konumsal ilişkiler ("masanın üzerine vazo koy")

### 2. Akıllı Soru-Cevap Diyalogları

AI artık belirsiz komutlarda kullanıcıya soru sorabiliyor.

#### Nasıl Çalışır:
- Belirsiz komutlar otomatik olarak tespit edilir
- AI açıklayıcı sorular sorar
- Kullanıcı cevap verir
- AI cevabı kullanarak kodu üretir

#### Örnek Diyalog:
```
Kullanıcı: "Bunu büyüt"
AI: "Hangi nesneyi büyütmemi istersiniz? Ve ne kadar büyütmeliyim?"
Kullanıcı: "Seçili küpü 2 katına çıkar"
AI: [Uygun kodu üretir]
```

#### Soru Sorulacak Durumlar:
- Belirsiz nesne referansları ("bunu", "şunu")
- Eksik parametreler (boyut, renk, konum belirtilmemiş)
- Genel komutlar ("bir şey oluştur", "değiştir")

### 3. Hibrit Çalışma Modu

İki sistem birlikte çalışarak daha akıllı sonuçlar üretir:

#### Senaryo 1: Soru Sonrası Çok Adımlı Görev
```
Kullanıcı: "Bir oda oluştur"
AI: "Nasıl bir oda istersiniz? Boyutları ve içeriği hakkında bilgi verebilir misiniz?"
Kullanıcı: "Orta boyutlu, içinde masa ve iki sandalye olan bir oturma odası"
AI: [Bu cevabı çok adımlı görev olarak planlar ve çalıştırır]
```

#### Senaryo 2: Karmaşık Soru Zinciri
```
Kullanıcı: "Mobilya ekle"
AI: "Hangi tür mobilya eklemek istersiniz?"
Kullanıcı: "Masa ve sandalye"
AI: "Kaç sandalye ve nasıl bir masa istersiniz?"
Kullanıcı: "4 sandalye ve büyük bir yemek masası"
AI: [Detaylı plan oluşturur ve çalıştırır]
```

## 🔧 Teknik Detaylar

### Yeni Fonksiyonlar (utilities.py)

#### `is_complex_task(prompt)`
- Komutun karmaşık olup olmadığını analiz eder
- Çoklu gösterge kelimeler arar
- Regex desenleri kullanır

#### `plan_complex_task(prompt, context, api_key, base_url, model)`
- AI kullanarak görevi adımlara böler
- JSON formatında adım listesi döner
- Hata durumunda None döner

#### `execute_step_by_step(steps, context, system_prompt, api_key, base_url, model)`
- Adımları sırayla çalıştırır
- Her adım için ayrı API çağrısı yapar
- Başarı/başarısızlık durumlarını takip eder

#### `is_question_response(response_text)`
- AI cevabının soru olup olmadığını analiz eder
- Soru göstergelerini arar
- Kod yoğunluğunu kontrol eder

#### `generate_enhanced_blender_code()`
- Ana fonksiyon, tüm yeni özellikleri koordine eder
- Karmaşık görev tespiti yapar
- Soru-cevap durumunu yönetir

### Operatör Güncellemeleri (__init__.py)

#### BLENDPRO_OT_Execute Sınıfı
- `_waiting_for_answer` ve `_current_question` değişkenleri eklendi
- `_background_process()` enhanced fonksiyonu kullanıyor
- `modal()` fonksiyonu soru-cevap durumlarını yönetiyor
- `execute()` fonksiyonu önceki soru bağlamını koruyor

## 📊 Performans ve Optimizasyon

### API Çağrısı Optimizasyonu
- Çok adımlı görevlerde her adım için daha kısa timeout (30s)
- Daha düşük temperature (0.3) planlama için
- Daha az token (800) adım başına

### Hata Yönetimi
- Başarısız adımlar diğer adımları durdurmaz
- Detaylı hata raporlama
- Fallback mekanizmaları

### Bellek Yönetimi
- Chat geçmişi otomatik kaydediliyor
- Soru-cevap durumu korunuyor
- Thread-safe işlemler

## 🎯 Kullanım İpuçları

### En İyi Sonuçlar İçin:
1. **Açık komutlar verin**: "Kırmızı küp oluştur" > "Bir şey oluştur"
2. **Karmaşık görevleri doğal dilde ifade edin**: "Masa ve 4 sandalye ile yemek odası yap"
3. **AI sorularını detaylı cevaplayın**: Boyut, renk, konum gibi detayları belirtin
4. **Console çıktısını takip edin**: Debug mesajları yararlı bilgiler içerir

### Sorun Giderme:
1. **Çok adımlı planlama çalışmıyorsa**: API anahtarını ve model seçimini kontrol edin
2. **Sorular sorulmuyorsa**: Komutunuzu daha belirsiz hale getirin
3. **Adımlar başarısız oluyorsa**: Her adımı ayrı ayrı test edin

## 🔄 Geriye Uyumluluk

Tüm eski komutlar ve özellikler çalışmaya devam ediyor:
- Basit komutlar eskisi gibi çalışır
- Mevcut chat geçmişi korunur
- API ayarları değişmez
- Kod önizleme sistemi aynı

## 📈 Gelecek Geliştirmeler

Potansiyel iyileştirmeler:
- Görsel sahne analizi ile daha akıllı planlama
- Kullanıcı tercihlerini öğrenme
- Daha gelişmiş hata kurtarma
- Paralel adım çalıştırma
- Adım önizleme ve onaylama

## 🐛 Bilinen Sınırlamalar

- Çok karmaşık görevler (10+ adım) yavaş olabilir
- API rate limit'leri çok adımlı görevleri etkileyebilir
- Bazı belirsiz komutlar yanlış sınıflandırılabilir
- Soru-cevap zinciri çok uzarsa performans düşebilir
