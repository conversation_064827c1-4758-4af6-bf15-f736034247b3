# BlendPro AI İyileştirmeleri Test Örnekleri

Bu dosya, BlendPro eklentisine eklenen yeni özellikleri test etmek için örnek komutlar içerir.

## 1. Çok Adımlı Görev Planlama Testi

### Basit Çok Adımlı Görev
```
Küçük bir masa ve sandalye oluştur
```
**Beklenen Davranış:** Sistem bu komutu iki adıma ayırmalı:
1. Masa oluşturma
2. Sandalye oluşturma

### Karmaşık Oda Oluşturma
```
Alçak tavanlı, ahşap dokulu, içinde bir masa ve sandalye olan basit bir oda yap
```
**Beklenen Davranış:** Sistem bu komutu şu adımlara ayırmalı:
1. Oda için küp oluştur ve ölçeklendir
2. Ahşap materyal oluştur
3. Ahşap materyali duvarlara ata
4. Masa <PERSON>
5. Sandalye oluştur
6. Nesneleri odanın içine yerleştir

### Çoklu Nesne ve Materyal
```
Metal bir masa, ahşap sandalye ve cam bir vazo oluştur
```
**Beklenen Davranış:** Sistem farklı materyaller ve nesneler için ayrı adımlar oluşturmalı.

## 2. Soru-Cevap Diyalog Testi

### Belirsiz Komutlar
```
Bunu büyüt
```
**Beklenen Davranış:** AI şu şekilde soru sormalı:
"Hangi nesneyi büyütmemi istersiniz? Ve ne kadar büyütmeliyim?"

```
Rengi değiştir
```
**Beklenen Davranış:** AI hangi nesnenin rengini hangi renge değiştireceğini sormalı.

```
Materyal ekle
```
**Beklenen Davranış:** AI hangi nesneye hangi tür materyal ekleneceğini sormalı.

### Eksik Bilgi İçeren Komutlar
```
Bir şey oluştur
```
**Beklenen Davranış:** AI ne tür nesne oluşturulacağını sormalı.

```
Hareket ettir
```
**Beklenen Davranış:** AI hangi nesneyi nereye hareket ettireceğini sormalı.

## 3. Hibrit Test Senaryoları

### Soru Sonrası Çok Adımlı Görev
1. İlk komut: `Bir oda oluştur`
2. AI sorusu: "Nasıl bir oda istersiniz? Boyutları ve içeriği hakkında bilgi verebilir misiniz?"
3. Cevap: `Orta boyutlu, içinde masa ve iki sandalye olan bir oturma odası`
4. **Beklenen Davranış:** AI bu cevabı çok adımlı görev olarak planlamalı.

### Karmaşık Soru-Cevap Zinciri
1. İlk komut: `Mobilya ekle`
2. AI sorusu: "Hangi tür mobilya eklemek istersiniz?"
3. Cevap: `Masa ve sandalye`
4. AI sorusu: "Kaç sandalye ve nasıl bir masa istersiniz?"
5. Cevap: `4 sandalye ve büyük bir yemek masası`
6. **Beklenen Davranış:** AI bu bilgileri kullanarak çok adımlı plan oluşturmalı.

## 4. Test Prosedürü

### Adım 1: Eklentiyi Yükleyin
1. Blender'ı açın
2. Edit > Preferences > Add-ons
3. BlendPro eklentisini etkinleştirin
4. API anahtarınızı ayarlayın

### Adım 2: Basit Testler
1. Yukarıdaki basit komutları tek tek deneyin
2. Console'da debug mesajlarını kontrol edin
3. Chat geçmişinde adımların görünüp görünmediğini kontrol edin

### Adım 3: Karmaşık Testler
1. Çok adımlı görevleri test edin
2. Her adımın ayrı ayrı çalıştırıldığını kontrol edin
3. Başarısız adımların raporlandığını kontrol edin

### Adım 4: Soru-Cevap Testleri
1. Belirsiz komutlar verin
2. AI'ın soru sorup sormadığını kontrol edin
3. Cevap verdikten sonra doğru kodu üretip üretmediğini kontrol edin

## 5. Beklenen Çıktılar

### Console Mesajları
- `Complex task detected: [komut]`
- `Task planned into X steps`
- `Executing step X/Y: [adım]`
- `Step X completed successfully`
- `AI asked: [soru]`

### Chat Geçmişi
- Çok adımlı görevlerde: "Multi-step task completed (X steps)"
- Sorularda: AI'ın sorusu chat'te görünmeli
- Cevaplarda: "Previous question: ... My answer: ..." formatı

### Kod Önizleme
- Çok adımlı görevlerde her adım için yorum satırları
- Başarısız adımlar için "# FAILED:" yorumları
- Tek adımlı görevlerde normal kod

## 6. Sorun Giderme

### Çok Adımlı Planlama Çalışmıyorsa
- `is_complex_task()` fonksiyonunun doğru çalıştığını kontrol edin
- API bağlantısının çalıştığını test edin
- Console'da hata mesajlarını kontrol edin

### Soru-Cevap Çalışmıyorsa
- `is_question_response()` fonksiyonunu test edin
- Chat geçmişinin doğru kaydedildiğini kontrol edin
- Modal dialog'un doğru çalıştığını kontrol edin

### Genel Sorunlar
- API anahtarının doğru ayarlandığını kontrol edin
- Model seçiminin uygun olduğunu kontrol edin
- İnternet bağlantısını kontrol edin
