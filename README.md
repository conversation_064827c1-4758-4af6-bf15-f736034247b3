# BlendPro - Advanced AI-Powered Blender Assistant

![Whisk_18340afb71](https://github.com/user-attachments/assets/f9d1489e-07f8-4e4d-b2de-546bee7002e3)



**Author:** [inkbytefo](https://github.com/inkbytefo)

BlendPro is a powerful Blender addon that integrates AI language models directly into your 3D workflow. Generate complex Blender Python scripts using natural language commands, with advanced features like code preview, auto-save, export/import, and undo functionality.

## ✨ Key Features

- 🤖 **AI-Powered Code Generation**: Generate Blender Python scripts using natural language
- ⚙️ **Advanced AI Configuration**: Fine-tune temperature, max tokens, and top-p parameters
- 💾 **Auto-Save Chat History**: Automatically saves and restores your conversation history
- 📁 **Export/Import Sessions**: Backup and share your chat sessions as JSON files
- 👁️ **Code Preview**: Review generated code before execution for safety
- ↩️ **Undo/Redo System**: Automatic scene backups with one-click undo functionality
- 🔧 **Multi-Model Support**: Works with OpenAI, Anthropic, and local models
- 🎯 **User-Friendly Interface**: Clean, intuitive UI integrated into Blender's sidebar

## 🚀 What's New in BlendPro

This enhanced version includes major improvements over the original BlenderGPT:
- **Smart Code Preview**: See exactly what code will run before execution
- **Persistent Chat History**: Never lose your conversations again
- **Advanced AI Controls**: Professional-grade AI parameter tuning
- **Backup & Recovery**: Automatic scene state management with undo capability
- **Session Management**: Export and import your chat sessions

### 🔥 NEW: Smart AI Context System
- **Automatic Context Detection**: AI automatically detects when scene context is needed
- **Multi-Level Intelligence**: 4 levels of context from basic to full vision analysis
- **Seamless Integration**: Context works behind the scenes without user intervention
- **Performance Optimized**: Smart caching and level detection for optimal performance
- **Professional Analysis**: Industry-standard scene evaluation integrated into AI responses

### 🧠 Context Intelligence Levels:
- **Level 0**: No context - for general Blender questions
- **Level 1**: Basic scene data - object counts, materials, lights
- **Level 2**: Detailed analysis - full scene data extraction
- **Level 3**: Vision analysis - screenshots + comprehensive scene understanding

## 📦 Installation

### Basic Installation
1. Download the latest release from [GitHub](https://github.com/inkbytefo/BlendPro)
2. Open Blender, go to `Edit > Preferences > Add-ons > Install`
3. Select the downloaded ZIP file and click `Install Add-on`
4. Enable the add-on by checking the checkbox next to `GPT-4 Blender Assistant`
5. Configure your API settings in the addon preferences

### Vision System Dependencies (Optional but Recommended)
For full AI Vision capabilities, install these Python packages in Blender's Python environment:

**Method 1: Using Blender's Python Console**
```python
import subprocess
import sys
subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'opencv-python>=4.8.0'])
subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pillow>=10.0.0'])
subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'numpy>=1.24.0'])
```

**Method 2: Command Line (Advanced)**
Navigate to Blender's Python directory and run:
```bash
# Windows
cd "C:\Program Files\Blender Foundation\Blender [version]\[version]\python\bin"
python -m pip install opencv-python pillow numpy

# macOS
cd "/Applications/Blender.app/Contents/Resources/[version]/python/bin"
./python -m pip install opencv-python pillow numpy

# Linux
cd "/usr/share/blender/[version]/python/bin"
./python -m pip install opencv-python pillow numpy
```

## ⚙️ Configuration

### API Setup
1. Go to `Edit > Preferences > Add-ons > GPT-4 Blender Assistant`
2. Enter your **API Key** (OpenAI, Anthropic, or compatible service)
3. Set **Base URL** if using custom endpoints (optional)
4. Configure **AI Parameters**:
   - **Temperature** (0.0-2.0): Controls creativity vs consistency
   - **Max Tokens** (1-4000): Maximum response length
   - **Top P** (0.0-1.0): Controls response diversity

### Supported Models
- **OpenAI**: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo, GPT-4o, GPT-4o Mini
- **Custom Models**: Any OpenAI-compatible API
- **Local Models**: Self-hosted AI models

## 🎮 Usage

### Basic AI Assistant
1. Open the **3D View** sidebar (press `N` if not visible)
2. Navigate to the **BlendPro Assistant** tab
3. Type your command: *"Create a spiral staircase with 20 steps"*
4. Click **Execute** to generate code
5. Review the code in the **Preview Dialog**
6. Click **Execute Code** to run it in Blender

### Smart AI Context System
1. **Automatic Operation**: Context system works automatically - no manual intervention needed
2. **Intelligent Detection**: AI detects context level based on your question keywords
3. **Seamless Experience**: Just ask questions naturally, AI handles the rest
4. **Performance Optimized**: Smart caching ensures fast responses

### Context Triggers:
- **Basic Context**: "create", "add", "make", "modify" → Gets object counts and basic info
- **Detailed Context**: "current", "scene", "visible", "these objects" → Full scene analysis
- **Vision Context**: "see", "look", "analyze", "composition" → Screenshots + scene data

### Example Commands

**Basic Commands:**
- "Create 10 random cubes between -5 and 5 on all axes"
- "Add a material with red color to the selected object"
- "Create a camera looking at the origin from 10 units away"
- "Generate a procedural landscape with noise texture"

**Smart Context Examples:**

**Level 1 (Basic Context):**
- "Create 10 cubes in my scene" → Gets object count, suggests positioning
- "Add a material to the selected object" → Knows current materials

**Level 2 (Detailed Context):**
- "Improve the lighting in my current scene" → Analyzes existing lights and setup
- "What objects are in my scene?" → Lists all objects with details

**Level 3 (Vision Context):**
- "Analyze my scene composition" → Takes screenshot + full scene analysis
- "What do you see in my viewport?" → Visual analysis with technical data
- "How can I improve the visual balance?" → Composition analysis with suggestions

## 🛠️ Advanced Features

### Chat Management
- **Auto-Save**: Conversations automatically saved and restored
- **Export Chat**: Save sessions as JSON files for backup
- **Import Chat**: Load previous conversations
- **Clear Chat**: Start fresh while keeping backups

### Safety & Recovery
- **Code Preview**: Always review before execution
- **Auto Backup**: Scene state saved before each operation
- **Undo Last**: One-click recovery from mistakes
- **Backup Management**: View and cleanup old backups

## 🔧 Requirements

- **Blender**: 3.1 or later
- **API Key**: OpenAI, Anthropic, or compatible service
- **Internet**: For API calls (unless using local models)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Original BlenderGPT concept and foundation
- OpenAI for providing powerful language models
- Blender Foundation for the amazing 3D software
- The open-source community for inspiration and support

---

**Developed by [inkbytefo](https://github.com/inkbytefo)** | **Repository**: [BlendPro](https://github.com/inkbytefo/BlendPro)
