__all__: list[str] = []

import cv2


# Functions
def getBackendName(api: cv2.VideoCaptureAPIs) -> str: ...

def getBackends() -> _typing.Sequence[cv2.VideoCaptureAPIs]: ...

def getCameraBackendPluginVersion(api: cv2.VideoCaptureAPIs) -> tuple[str, int, int]: ...

def getCameraBackends() -> _typing.Sequence[cv2.VideoCaptureAPIs]: ...

def getStreamBackendPluginVersion(api: cv2.VideoCaptureAPIs) -> tuple[str, int, int]: ...

def getStreamBackends() -> _typing.Sequence[cv2.VideoCaptureAPIs]: ...

def getStreamBufferedBackendPluginVersion(api: cv2.VideoCaptureAPIs) -> tuple[str, int, int]: ...

def getStreamBufferedBackends() -> _typing.Sequence[cv2.VideoCaptureAPIs]: ...

def getWriterBackendPluginVersion(api: cv2.VideoCaptureAPIs) -> tuple[str, int, int]: ...

def getWriterBackends() -> _typing.Sequence[cv2.VideoCaptureAPIs]: ...

def hasBackend(api: cv2.VideoCaptureAPIs) -> bool: ...

def isBackendBuiltIn(api: cv2.VideoCaptureAPIs) -> bool: ...


