#!/usr/bin/env python3
"""
Test script for BlendPro improvements
Tests the enhanced multi-step planning system
"""

import sys
import os

# Add the current directory to Python path for testing
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complex_task_detection():
    """Test the is_complex_task function"""
    from utilities import is_complex_task
    
    # Test cases
    test_cases = [
        ("Create a cube", False),  # Simple task
        ("Create a table and chair", True),  # Complex task
        ("Make a room with furniture", True),  # Complex task
        ("Scale the object", False),  # Simple task
        ("Create a wooden table inside a room", True),  # Complex task
    ]
    
    print("Testing complex task detection:")
    for prompt, expected in test_cases:
        result = is_complex_task(prompt)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{prompt}' -> {result} (expected: {expected})")

def test_object_extraction():
    """Test the extract_created_objects_from_code function"""
    from utilities import extract_created_objects_from_code
    
    test_code = """
import bpy

# Create a cube
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 0))
bpy.context.active_object.name = "Table"

# Create a cylinder
bpy.ops.mesh.primitive_cylinder_add(location=(2, 0, 0))
obj = bpy.context.active_object
obj.name = "Chair_Leg"

# Create a new object
mesh = bpy.data.meshes.new("CustomMesh")
obj = bpy.data.objects.new("CustomObject", mesh)
"""
    
    print("\nTesting object extraction:")
    objects = extract_created_objects_from_code(test_code)
    print(f"  Extracted objects: {objects}")

def test_plan_formatting():
    """Test the format_step_plan_for_user function"""
    from utilities import format_step_plan_for_user
    
    test_steps = [
        "Create a cube for the table top",
        "Scale the cube to table size",
        "Create a cylinder for table leg",
        "Duplicate the leg 4 times",
        "Position the legs correctly"
    ]
    
    print("\nTesting plan formatting:")
    formatted = format_step_plan_for_user(test_steps)
    print("  Formatted plan:")
    for line in formatted.split('\n'):
        print(f"    {line}")

def test_plan_approval_detection():
    """Test the detect_plan_approval function"""
    from utilities import detect_plan_approval
    
    test_cases = [
        ("yes", "approve"),
        ("evet", "approve"),
        ("ok proceed", "approve"),
        ("no", "reject"),
        ("hayır", "reject"),
        ("use single step", "reject"),
        ("maybe later", "unclear"),
        ("what do you think?", "unclear"),
    ]
    
    print("\nTesting plan approval detection:")
    for input_text, expected in test_cases:
        result = detect_plan_approval(input_text)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{input_text}' -> {result} (expected: {expected})")

def test_critical_failure_detection():
    """Test the is_critical_step_failure function"""
    from utilities import is_critical_step_failure
    
    test_cases = [
        ("Create cube", "API key not found", True),
        ("Scale object", "Object not found", False),
        ("Add material", "Connection timeout", True),
        ("Position object", "Invalid parameter", False),
        ("Create light", "Rate limit exceeded", True),
    ]
    
    print("\nTesting critical failure detection:")
    for step, error, expected in test_cases:
        result = is_critical_step_failure(step, error)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{step}' + '{error}' -> {result} (expected: {expected})")

if __name__ == "__main__":
    print("BlendPro Improvements Test Suite")
    print("=" * 40)
    
    try:
        test_complex_task_detection()
        test_object_extraction()
        test_plan_formatting()
        test_plan_approval_detection()
        test_critical_failure_detection()
        
        print("\n" + "=" * 40)
        print("All tests completed!")
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure you're running this from the BlendPro directory")
    except Exception as e:
        print(f"Test error: {e}")
